import {Model, Optional, DataTypes} from "sequelize";
import {sequelize as conn} from "../../config/database";
import {iStatisticsOfAPI} from "./iStatisticsOfAPI";
import {EndpointType, LatestApi} from "./LatestApi";

type StatisticsOfAPICreationAttributes = Optional<iStatisticsOfAPI, "id" | "request_url">;

class StatisticsOfAPI extends Model<iStatisticsOfAPI, StatisticsOfAPICreationAttributes> {
    declare id?: number;
    declare request_date: Date;
    declare latest_api: LatestApi;
    declare api_credit_consumption: number;
    declare request_url?: string; // Made optional to match interface
    declare endpoint_type?: EndpointType;
}

StatisticsOfAPI.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false,
        },
        request_date: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        latest_api: {
            type: DataTypes.ENUM("eodhd", "twelve_data", "others"),
            allowNull: false,
        },
        api_credit_consumption: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        request_url: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        endpoint_type: {
            type: DataTypes.ENUM("symbol_list", "fundamentals", "dividends", "splits", "price"),
            allowNull: true,
        },
    },
    {
        timestamps: false,
        sequelize: conn,
        tableName: "statistics_of_apis",
    },
);

export {StatisticsOfAPI};
